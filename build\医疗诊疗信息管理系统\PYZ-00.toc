('E:\\code\\yiyaoxinxichuli\\build\\医疗诊疗信息管理系统\\PYZ-00.pyz',
 [('__future__',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_colorize',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_colorize.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_ios_support',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_ios_support.py',
   'PYMODULE'),
  ('_opcode_metadata',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('_py_abc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyrepl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\__init__.py',
   'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.base_eventqueue',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\base_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.commands',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\commands.py',
   'PYMODULE'),
  ('_pyrepl.completing_reader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\console.py',
   'PYMODULE'),
  ('_pyrepl.curses',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\curses.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\input.py',
   'PYMODULE'),
  ('_pyrepl.keymap',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\keymap.py',
   'PYMODULE'),
  ('_pyrepl.main',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\main.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\pager.py',
   'PYMODULE'),
  ('_pyrepl.reader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\reader.py',
   'PYMODULE'),
  ('_pyrepl.readline',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\readline.py',
   'PYMODULE'),
  ('_pyrepl.simple_interact',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\trace.py',
   'PYMODULE'),
  ('_pyrepl.types',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\types.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\utils.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_pyrepl.windows_eventqueue',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\windows_eventqueue.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\calendar.py',
   'PYMODULE'),
  ('cmd', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\codeop.py', 'PYMODULE'),
  ('concurrent',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('curses',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\curses\\has_key.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\datetime.py',
   'PYMODULE'),
  ('dateutil',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\difflib.py',
   'PYMODULE'),
  ('dis', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\dis.py', 'PYMODULE'),
  ('doctest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\doctest.py',
   'PYMODULE'),
  ('email',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fileinput',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\fileinput.py',
   'PYMODULE'),
  ('fnmatch',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\hmac.py', 'PYMODULE'),
  ('html',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\http\\server.py',
   'PYMODULE'),
  ('importlib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('mkl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\mkl\\__init__.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\numbers.py',
   'PYMODULE'),
  ('numexpr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numexpr\\__init__.py',
   'PYMODULE'),
  ('numexpr.cpuinfo',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numexpr\\cpuinfo.py',
   'PYMODULE'),
  ('numexpr.expressions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numexpr\\expressions.py',
   'PYMODULE'),
  ('numexpr.necompiler',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numexpr\\necompiler.py',
   'PYMODULE'),
  ('numexpr.tests',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numexpr\\tests\\__init__.py',
   'PYMODULE'),
  ('numexpr.tests.test_numexpr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numexpr\\tests\\test_numexpr.py',
   'PYMODULE'),
  ('numexpr.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numexpr\\utils.py',
   'PYMODULE'),
  ('numexpr.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numexpr\\version.py',
   'PYMODULE'),
  ('numpy',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\ctypeslib\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib._ctypeslib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\ctypeslib\\_ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._format_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_format_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('packaging',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._abc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('pathlib._local',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('pdb', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pickle.py', 'PYMODULE'),
  ('pickletools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pickletools.py',
   'PYMODULE'),
  ('pkg_resources',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\plistlib.py',
   'PYMODULE'),
  ('pprint', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pytz',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\random.py', 'PYMODULE'),
  ('rlcompleter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._itertools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_itertools.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils._vendor',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._vendor.packaging',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._vendor.packaging._elffile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._distutils._vendor.packaging._manylinux',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._distutils._vendor.packaging._musllinux',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._distutils._vendor.packaging._structures',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._distutils._vendor.packaging.tags',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._distutils._vendor.packaging.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._distutils._vendor.packaging.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py38',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat.py38',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat.py39',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.functional',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\functional.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.future',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\future\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.future.adapters',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\future\\adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\ordered_set\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site.py', 'PYMODULE'),
  ('six',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('socket', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\socketserver.py',
   'PYMODULE'),
  ('sqlite3',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\sysconfig\\__init__.py',
   'PYMODULE'),
  ('tarfile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\threading.py',
   'PYMODULE'),
  ('tkinter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\token.py', 'PYMODULE'),
  ('tokenize',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tomllib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\typing.py', 'PYMODULE'),
  ('unittest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('uuid', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('wheel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\__init__.py',
   'PYMODULE'),
  ('wheel.cli',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('wheel.metadata',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\metadata.py',
   'PYMODULE'),
  ('wheel.util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\util.py',
   'PYMODULE'),
  ('wheel.vendored',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('xml',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\zipimport.py',
   'PYMODULE')])
