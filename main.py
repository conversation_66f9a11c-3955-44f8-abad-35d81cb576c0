import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import pandas as pd
import os
import glob
from difflib import SequenceMatcher
from typing import Dict, List, Tuple, Optional
import threading
import datetime

"""
正式版

"""


class MedicalRecordsGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("诊所/医生专属信息管理系统")
        self.root.geometry("1200x700")  # 稍微增加宽度以适应新布局
        self.root.resizable(True, True)
        
        # 初始化数据
        self.manager = None
        self.excel_folder_path = ""
        self.current_search_results = {}  # 存储当前搜索结果的详细数据
        
        # 设置样式
        self.setup_styles()
        
        # 创建界面
        self.create_widgets()
        
        # 初始化状态
        self.update_status("请选择Excel文件夹并加载数据")
    
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        # 定义全局字体
        self.default_font = ('SimHei', 18)  # 这里设置全局字体和大小
        style.configure('.', font=self.default_font)
        style.configure('Title.TLabel', font=('SimHei', 16, 'bold'))
        style.configure('Header.TLabel', font=('SimHei', 12, 'bold'))
        style.configure('Status.TLabel', foreground='blue')
        style.configure('Treeview', font=('SimHei', 14))  # 或更小
        style.configure('Treeview.Heading', font=('SimHei', 14, 'bold'))
        
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill='x', padx=10, pady=5)
        
        title_label = ttk.Label(title_frame, text="诊所/医生专属信息管理系统", style='Title.TLabel')
        title_label.pack()
        
        # 创建主要区域
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # 左侧区域 - 添加新病例
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side='left', fill='y', padx=(0, 5))
        
        # 右侧区域 - 文件管理和搜索
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))
        
        # 创建各个功能区域
        self.create_add_record_section(left_frame)  # 添加新病例放到左边
        self.create_file_section(right_frame)       # 文件管理放到右边
        self.create_search_section(right_frame)     # 搜索功能放到右边
        self.create_results_section(right_frame)    # 结果显示放到右边
        self.create_status_section(self.root)
    
    def create_file_section(self, parent):
        """创建文件管理区域"""
        file_frame = ttk.LabelFrame(parent, text="文件管理", padding=10)
        file_frame.pack(fill='x', pady=(0, 10))
        
        # 文件夹选择
        folder_frame = ttk.Frame(file_frame)
        folder_frame.pack(fill='x', pady=(0, 5))
        
        ttk.Label(folder_frame, text="Excel文件夹:").pack(side='left')
        
        self.folder_var = tk.StringVar()
        self.folder_entry = ttk.Entry(folder_frame, textvariable=self.folder_var, state='readonly')
        self.folder_entry.pack(side='left', fill='x', expand=True, padx=(5, 5))
        
        ttk.Button(folder_frame, text="选择文件夹", command=self.select_folder).pack(side='right')
        
        # 加载按钮
        button_frame = ttk.Frame(file_frame)
        button_frame.pack(fill='x')
        
        self.load_button = ttk.Button(button_frame, text="加载Excel文件", command=self.load_files)
        self.load_button.pack(side='left')
        
        ttk.Button(button_frame, text="生成测试数据", command=self.generate_test_data).pack(side='left', padx=(10, 0))
        
        # 文件信息显示
        self.file_info_var = tk.StringVar(value="未加载文件")
        ttk.Label(file_frame, textvariable=self.file_info_var, style='Status.TLabel').pack(anchor='w', pady=(5, 0))
    
    def create_search_section(self, parent):
        """创建搜索区域"""
        search_frame = ttk.LabelFrame(parent, text="查找相似病例", padding=10)
        search_frame.pack(fill='x', pady=(0, 10))
        
        # 姓名输入
        ttk.Label(search_frame, text="姓名:").grid(row=0, column=0, sticky='w', pady=2)
        self.search_name_var = tk.StringVar()
        name_entry = ttk.Entry(search_frame, textvariable=self.search_name_var, width=50)
        name_entry.grid(row=0, column=1, sticky='ew', padx=(5, 0), pady=2)
        
        # 症状输入
        ttk.Label(search_frame, text="症状:").grid(row=1, column=0, sticky='w', pady=2)
        self.symptoms_var = tk.StringVar()
        symptoms_entry = ttk.Entry(search_frame, textvariable=self.symptoms_var, width=50)
        symptoms_entry.grid(row=1, column=1, sticky='ew', padx=(5, 0), pady=2)
        
        # 病情输入
        ttk.Label(search_frame, text="病情:").grid(row=2, column=0, sticky='w', pady=2)
        self.condition_var = tk.StringVar()
        condition_entry = ttk.Entry(search_frame, textvariable=self.condition_var, width=50)
        condition_entry.grid(row=2, column=1, sticky='ew', padx=(5, 0), pady=2)
        
        # 搜索按钮
        search_button = ttk.Button(search_frame, text="搜索病例", command=self.search_cases)
        search_button.grid(row=3, column=0, columnspan=2, pady=(10, 0))
        
        # 配置网格权重
        search_frame.columnconfigure(1, weight=1)
        
        # 绑定回车键
        name_entry.bind('<Return>', lambda e: self.search_cases())
        symptoms_entry.bind('<Return>', lambda e: self.search_cases())
        condition_entry.bind('<Return>', lambda e: self.search_cases())
    
    def create_results_section(self, parent):
        """创建结果显示区域"""
        results_frame = ttk.LabelFrame(parent, text="搜索结果", padding=10)
        results_frame.pack(fill='both', expand=True)
        
        # 创建Treeview显示结果
        columns = ('就诊时间', '姓名', '年龄', '症状', '病情', '处方', '来源文件')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=15)
        
        # 设置列标题和宽度
        column_widths = {'就诊时间': 180, '姓名': 80, '年龄': 50, '症状': 180, '病情': 150, '处方': 200, '来源文件': 100}
        
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # 添加滚动条
        results_scrollbar_y = ttk.Scrollbar(results_frame, orient='vertical', command=self.results_tree.yview)
        results_scrollbar_x = ttk.Scrollbar(results_frame, orient='horizontal', command=self.results_tree.xview)
        
        self.results_tree.configure(yscrollcommand=results_scrollbar_y.set, xscrollcommand=results_scrollbar_x.set)
        
        # 布局
        self.results_tree.grid(row=0, column=0, sticky='nsew')
        results_scrollbar_y.grid(row=0, column=1, sticky='ns')
        results_scrollbar_x.grid(row=1, column=0, sticky='ew')
        
        # 配置网格权重
        results_frame.rowconfigure(0, weight=1)
        results_frame.columnconfigure(0, weight=1)
        
        # 双击查看详情
        self.results_tree.bind('<Double-1>', self.show_case_details)
    
    def create_add_record_section(self, parent):
        """创建添加新病例区域"""
        add_frame = ttk.LabelFrame(parent, text="添加新病例", padding=10)
        add_frame.pack(fill='both', expand=True)
        
        # 输入字段
        fields = [
            ('姓名:', 'name_var'),
            ('年龄:', 'age_var'),
            ('联系方式:', 'contact_var'),
            ('症状:', 'symptoms_add_var'),
            ('病情:', 'condition_add_var'),
            ('处方:', 'prescription_var'),
            # ('就诊时间:', 'visit_time_var'),
        ]
        
        self.add_vars = {}
        
        for i, (label, var_name) in enumerate(fields):
            ttk.Label(add_frame, text=label).grid(row=i, column=0, sticky='nw', pady=2)  # 改为nw对齐
            
            self.add_vars[var_name] = tk.StringVar()
            
            if var_name in ['symptoms_add_var', 'prescription_var']:
                if var_name == 'prescription_var':
                    # 只修改处方输入框的大小
                    entry = tk.Text(add_frame, height=10, width=50, wrap='word', font=self.default_font)
                else:
                    entry = tk.Text(add_frame, height=5, width=45, wrap='word', font=self.default_font)
                entry.grid(row=i, column=1, sticky='ew', padx=(5, 0), pady=2)
                setattr(self, f'{var_name}_widget', entry)
            else:
                # 单行文本框 - 增加宽度
                entry = ttk.Entry(add_frame, textvariable=self.add_vars[var_name], width=30)
                entry.grid(row=i, column=1, sticky='ew', padx=(5, 0), pady=2)
        
        # 按钮组
        button_frame = ttk.Frame(add_frame)
        button_frame.grid(row=len(fields), column=0, columnspan=2, pady=(20, 0), sticky='ew')
        
        # 添加按钮
        add_button = ttk.Button(button_frame, text="添加病例", command=self.add_record)
        add_button.pack(side='top', fill='x', pady=(0, 5))
        
        # 清空按钮
        clear_button = ttk.Button(button_frame, text="清空输入", command=self.clear_add_form)
        clear_button.pack(side='top', fill='x')
        
        # 配置网格权重
        add_frame.columnconfigure(1, weight=1)
    
    def create_status_section(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill='x', side='bottom', padx=10, pady=(0, 5))
        
        self.status_var = tk.StringVar()
        status_label = ttk.Label(status_frame, textvariable=self.status_var, relief='sunken', anchor='w')
        status_label.pack(fill='x')
    
    def select_folder(self):
        """选择Excel文件夹"""
        folder = filedialog.askdirectory(title="选择包含Excel文件的文件夹")
        if folder:
            self.excel_folder_path = folder
            self.folder_var.set(folder)
            self.update_status(f"已选择文件夹: {folder}")
    
    def load_files(self):
        """加载Excel文件"""
        if not self.excel_folder_path:
            messagebox.showwarning("警告", "请先选择Excel文件夹!")
            return
        
        # 在后台线程中加载文件
        self.update_status("正在加载Excel文件...")
        self.load_button.config(state='disabled')
        
        def load_thread():
            try:
                self.manager = MedicalRecordsManager(self.excel_folder_path)
                success = self.manager.load_excel_files()
                
                if success:
                    file_count = len([f for f in os.listdir(self.excel_folder_path) 
                                    if f.endswith(('.xlsx', '.xls'))])
                    record_count = len(self.manager.all_records)
                    
                    self.root.after(0, lambda: self.file_info_var.set(
                        f"已加载 {file_count} 个Excel文件，共 {record_count} 条记录"
                    ))
                    self.root.after(0, lambda: self.update_status("文件加载完成"))
                else:
                    self.root.after(0, lambda: messagebox.showerror("错误", "加载文件失败!"))
                    self.root.after(0, lambda: self.update_status("加载失败"))
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"加载文件时出错: {str(e)}"))
                self.root.after(0, lambda: self.update_status("加载出错"))
            finally:
                self.root.after(0, lambda: self.load_button.config(state='normal'))
        
        threading.Thread(target=load_thread, daemon=True).start()
    
    def search_cases(self):
        """搜索相似病例"""
        if not self.manager:
            messagebox.showwarning("警告", "请先加载Excel文件!")
            return
        
        name = self.search_name_var.get().strip()
        symptoms = self.symptoms_var.get().strip()
        condition = self.condition_var.get().strip()
        
        if not (name or symptoms or condition):
            messagebox.showwarning("警告", "请至少输入姓名、症状或病情中的一项!")
            return

        # 清空之前的结果
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        self.current_search_results.clear()
        self.update_status("正在搜索相似病例...")

        try:
            similar_cases = self.manager.search_similar_cases(name, symptoms, condition)
            
            if similar_cases:
                for i, (case, similarity) in enumerate(similar_cases):
                    # 生成唯一ID
                    result_id = f"result_{i}"
                    
                    # 存储完整的病例数据
                    self.current_search_results[result_id] = case
                    
                    # 限制文本长度以适应显示
                    symptoms_text = case.get('symptoms', '未知')[:50] + '...' if len(case.get('symptoms', '')) > 50 else case.get('symptoms', '未知')
                    condition_text = case.get('condition', '未知')[:30] + '...' if len(case.get('condition', '')) > 30 else case.get('condition', '未知')
                    prescription_text = case.get('prescription', '未知')[:50] + '...' if len(case.get('prescription', '')) > 50 else case.get('prescription', '未知')
                    
                    # 插入到Treeview，使用result_id作为item的ID
                    self.results_tree.insert('', 'end', iid=result_id, values=(
                        case.get('visit_time', '未知'),
                        case.get('name', '未知'),
                        case.get('age', '未知'),
                        symptoms_text,
                        condition_text,
                        prescription_text,
                        case.get('source_file', '未知')
                    ))
                
                self.update_status(f"找到 {len(similar_cases)} 个相似病例")
            else:
                self.update_status("没有找到相似的病例")
                messagebox.showinfo("提示", "没有找到相似的病例")
                
        except Exception as e:
            messagebox.showerror("错误", f"搜索时出错: {str(e)}")
            self.update_status("搜索出错")
    
    def show_case_details(self, event):
        """显示病例详情"""
        selection = self.results_tree.selection()
        if not selection:
            return
        
        # 获取选中项的ID
        item_id = selection[0]
        
        # 从存储的数据中获取完整病例信息
        if item_id not in self.current_search_results:
            messagebox.showerror("错误", "无法获取病例详细信息")
            return
        
        case = self.current_search_results[item_id]
        
        # 创建详情窗口
        detail_window = tk.Toplevel(self.root)
        detail_window.title("病例详情")
        detail_window.geometry("500x400")
        detail_window.resizable(True, True)
        
        # 创建滚动文本框
        text_widget = scrolledtext.ScrolledText(detail_window, wrap='word', padx=10, pady=10)
        text_widget.pack(fill='both', expand=True, padx=10, pady=10)
        text_widget.configure(font=('SimHei', 16))  
        # 显示详细信息
        details = f"""病例详细信息

姓名: {case.get('name', '未知')}
年龄: {case.get('age', '未知')}
联系方式: {case.get('contact', '未知')}

症状:
{case.get('symptoms', '未知')}

病情:
{case.get('condition', '未知')}

处方:
{case.get('prescription', '未知')}

就诊时间: {case.get('visit_time', '未知')}

来源文件: {case.get('source_file', '未知')}
"""
        
        text_widget.insert('1.0', details)
        text_widget.config(state='disabled')
    
    def add_record(self):
        """添加新病例"""
        if not self.manager:
            messagebox.showwarning("警告", "请先加载Excel文件!")
            return
        
        # 获取输入数据
        name = self.add_vars['name_var'].get().strip()
        age = self.add_vars['age_var'].get().strip()
        contact = self.add_vars['contact_var'].get().strip()
        condition = self.add_vars['condition_add_var'].get().strip()
        # 从Text widget获取多行文本
        symptoms = self.symptoms_add_var_widget.get('1.0', 'end-1c').strip()
        prescription = self.prescription_var_widget.get('1.0', 'end-1c').strip()

        # 自动获取当前时间
        visit_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 验证必填字段
        if not all([name, symptoms, condition, prescription]):
            messagebox.showwarning("警告", "请填写姓名、症状、病情和处方!")
            return

        try:
            # 传递visit_time
            success = self.manager.add_new_record(name, age, contact, symptoms, condition, prescription, visit_time)
            if success:
                messagebox.showinfo("成功", "新病例添加成功!")
                self.clear_add_form()
                self.update_status("新病例添加成功")
                
                # 更新文件信息
                record_count = len(self.manager.all_records)
                file_count = len([f for f in os.listdir(self.excel_folder_path) 
                                if f.endswith(('.xlsx', '.xls'))])
                self.file_info_var.set(f"已加载 {file_count} 个Excel文件，共 {record_count} 条记录")
            else:
                messagebox.showerror("错误", "添加失败!")
        except Exception as e:
            messagebox.showerror("错误", f"添加病例时出错: {str(e)}")
    
    def clear_add_form(self):
        """清空添加表单"""
        for var in self.add_vars.values():
            var.set("")
        
        self.symptoms_add_var_widget.delete('1.0', 'end')
        self.prescription_var_widget.delete('1.0', 'end')
    
    def generate_test_data(self):
        """生成测试数据"""
        self.update_status("正在生成测试数据...")
        
        def generate_thread():
            try:
                self.create_test_data_internal()
                self.root.after(0, lambda: messagebox.showinfo("成功", "测试数据生成完成!"))
                self.root.after(0, lambda: self.update_status("测试数据生成完成"))
                
                # 自动设置文件夹路径
                if not self.excel_folder_path:
                    records_path = os.path.join(os.getcwd(), "records")
                    if os.path.exists(records_path):
                        self.excel_folder_path = records_path
                        self.root.after(0, lambda: self.folder_var.set(records_path))
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"生成测试数据时出错: {str(e)}"))
                self.root.after(0, lambda: self.update_status("生成测试数据失败"))
        
        threading.Thread(target=generate_thread, daemon=True).start()
    
    def create_test_data_internal(self):
        """内部测试数据生成方法"""
        # 模拟病例数据
        test_data = [
            # 感冒相关病例
            {
                '姓名': '张三',
                '年龄': '28',
                '联系方式': '13800138001',
                '症状': '发热、咳嗽、流鼻涕、头痛',
                '病情': '普通感冒',
                '处方': '感冒灵颗粒 3次/日，布洛芬 发热时服用，多休息多喝水'
            },
            {
                '姓名': '李四',
                '年龄': '35',
                '联系方式': '13900139002',
                '症状': '咳嗽、发烧、鼻塞、喉咙痛',
                '病情': '上呼吸道感染',
                '处方': '阿莫西林 500mg 3次/日，复方甘草片 3次/日，多喝温水'
            },
            {
                '姓名': '王五',
                '年龄': '42',
                '联系方式': '13700137003',
                '症状': '流鼻涕、打喷嚏、轻微发热',
                '病情': '病毒性感冒',
                '处方': '维C银翘片 3次/日，扑热息痛 发热时服用，注意保暖'
            },
            
            # 胃病相关病例
            {
                '姓名': '赵六',
                '年龄': '45',
                '联系方式': '13600136004',
                '症状': '胃痛、恶心、腹胀、食欲不振',
                '病情': '慢性胃炎',
                '处方': '奥美拉唑 20mg 2次/日，胃复安 3次/日，饮食清淡规律'
            },
            {
                '姓名': '钱七',
                '年龄': '38',
                '联系方式': '13500135005',
                '症状': '上腹疼痛、烧心、反酸',
                '病情': '胃溃疡',
                '处方': '雷贝拉唑 20mg 2次/日，铝镁加混悬液 4次/日，避免辛辣食物'
            },
            {
                '姓名': '孙八',
                '年龄': '52',
                '联系方式': '13400134006',
                '症状': '胃部不适、饭后腹胀、嗳气',
                '病情': '功能性消化不良',
                '处方': '吗丁啉 3次/日，健胃消食片 3次/日，少食多餐'
            },
            
            # 高血压相关病例
            {
                '姓名': '周九',
                '年龄': '58',
                '联系方式': '13300133007',
                '症状': '头晕、头痛、心悸、疲劳',
                '病情': '高血压',
                '处方': '氨氯地平 5mg 1次/日，定期监测血压，低盐饮食'
            },
            {
                '姓名': '吴十',
                '年龄': '63',
                '联系方式': '13200132008',
                '症状': '头昏、胸闷、心慌',
                '病情': '高血压病',
                '处方': '依那普利 10mg 2次/日，阿司匹林 100mg 1次/日，适量运动'
            },
            
            # 糖尿病相关病例
            {
                '姓名': '郑十一',
                '年龄': '55',
                '联系方式': '13100131009',
                '症状': '多饮、多尿、多食、体重下降',
                '病情': '2型糖尿病',
                '处方': '二甲双胍 500mg 3次/日，定期监测血糖，控制饮食'
            },
            {
                '姓名': '陈十二',
                '年龄': '49',
                '联系方式': '13000130010',
                '症状': '口渴、尿频、视力模糊',
                '病情': '糖尿病',
                '处方': '格列齐特 80mg 2次/日，定期复查，运动疗法'
            }
        ]
        
        # 创建records文件夹
        os.makedirs("records", exist_ok=True)
        
        # 生成Excel文件
        df1 = pd.DataFrame(test_data[:6])
        df1.to_excel("records/内科病例.xlsx", index=False)
        
        df2 = pd.DataFrame(test_data[6:])
        df2.to_excel("records/专科病例.xlsx", index=False)
        
        # 生成不同格式的测试文件
        different_format_data = [
            {
                '患者姓名': '测试病人一',
                '年纪': '30',
                '电话号码': '13111131019',
                '主要症状': '头痛、发热、乏力',
                '疾病诊断': '病毒性感冒',
                '药物治疗': '对乙酰氨基酚 500mg 3次/日，多休息'
            },
            {
                '患者姓名': '测试病人二',
                '年纪': '25',
                '电话号码': '13000130020',
                '主要症状': '咳嗽、胸闷、低热',
                '疾病诊断': '支气管炎',
                '药物治疗': '阿奇霉素 250mg 1次/日，止咳糖浆 3次/日'
            }
        ]
        
        df3 = pd.DataFrame(different_format_data)
        df3.to_excel("records/格式测试.xlsx", index=False)
    
    def update_status(self, message):
        """更新状态栏"""
        self.status_var.set(message)
    
    def run(self):
        """运行GUI应用"""
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap('icon.ico')  # 如果有图标文件
            pass
        except:
            pass
        
        # 启动主循环
        self.root.mainloop()


class MedicalRecordsManager:
    def __init__(self, excel_folder_path: str = "records"):
        """
        医疗记录管理器
        
        Args:
            excel_folder_path: 存放Excel文件的文件夹路径
        """
        self.excel_folder_path = excel_folder_path
        self.all_records = []
        self.column_mappings = {}
        
        # 定义列名映射规则
        self.column_keywords = {
            'name': ['姓名', '患者', '病人', '名字', '患者姓名', '病人姓名'],
            'age': ['年龄', '岁', '年纪', '岁数'],
            'contact': ['电话', '手机', '联系方式', '联系电话', '手机号', '电话号码', '家长电话'],
            'symptoms': ['症状', '主诉', '表现', '症状表现', '临床表现', '主要症状'],
            'condition': ['病情', '诊断', '疾病', '病症', '诊断结果', '病情诊断', '疾病诊断'],
            'prescription': ['处方', '药方', '治疗', '用药', '治疗方案', '药物', '药物治疗'],
            'visit_time': ['就诊时间', '时间', '日期', '挂号时间', '诊疗时间']
        }
    
    def find_column_mapping(self, columns: List[str]) -> Dict[str, str]:
        """自动识别Excel列名对应的字段"""
        mapping = {}
        
        for field, keywords in self.column_keywords.items():
            best_match = ""
            best_score = 0
            
            for col in columns:
                for keyword in keywords:
                    # 检查是否包含关键词
                    if keyword in col:
                        score = len(keyword) / len(col)
                        if score > best_score:
                            best_score = score
                            best_match = col
                    
                    # 使用相似度匹配
                    similarity = SequenceMatcher(None, keyword, col).ratio()
                    if similarity > 0.6 and similarity > best_score:
                        best_score = similarity
                        best_match = col
            
            if best_match:
                mapping[field] = best_match
        
        return mapping
    
    def load_excel_files(self) -> bool:
        """加载文件夹中的所有Excel文件"""
        if not os.path.exists(self.excel_folder_path):
            return False
        
        excel_files = glob.glob(os.path.join(self.excel_folder_path, "*.xlsx")) + \
                     glob.glob(os.path.join(self.excel_folder_path, "*.xls"))
        
        if not excel_files:
            return False
        
        for excel_file in excel_files:
            try:
                # 读取Excel文件
                df = pd.read_excel(excel_file)
                
                if df.empty:
                    continue
                
                columns = df.columns.tolist()
                
                # 自动识别列映射
                mapping = self.find_column_mapping(columns)
                
                # 保存映射关系
                self.column_mappings[excel_file] = mapping
                
                # 处理数据
                for _, row in df.iterrows():
                    record = {}
                    for field, col_name in mapping.items():
                        if col_name in df.columns:
                            record[field] = str(row[col_name]) if pd.notna(row[col_name]) else ""
                    
                    if record:  # 只添加非空记录
                        record['source_file'] = os.path.basename(excel_file)
                        self.all_records.append(record)
                
            except Exception as e:
                continue
        
        return len(self.all_records) > 0
    
    def calculate_similarity(self, symptoms1: str, condition1: str, 
                           symptoms2: str, condition2: str) -> float:
        """计算两个病例的相似度"""
        # 症状相似度
        symptoms_sim = SequenceMatcher(None, symptoms1.lower(), symptoms2.lower()).ratio()
        
        # 病情相似度  
        condition_sim = SequenceMatcher(None, condition1.lower(), condition2.lower()).ratio()
        
        # 综合相似度 (症状权重0.6，病情权重0.4)
        total_sim = symptoms_sim * 0.6 + condition_sim * 0.4
        
        return total_sim
    
    def search_similar_cases(self, name: str, symptoms: str, condition: str, min_similarity: float = 0.3, max_results: int = 10):
        similar_cases = []
        for record in self.all_records:
            # 姓名完全匹配或部分匹配
            if name:
                record_name = record.get('name', '')
                if name not in record_name:
                    continue  # 跳过不匹配的
            # 症状/病情相似度
            record_symptoms = record.get('symptoms', '')
            record_condition = record.get('condition', '')
            if symptoms or condition:
                similarity = self.calculate_similarity(
                    symptoms, condition, record_symptoms, record_condition
                )
                if similarity >= min_similarity:
                    similar_cases.append((record, similarity))
            else:
                # 只按姓名查找时，similarity设为1
                similar_cases.append((record, 1.0))
        # 按相似度排序
        similar_cases.sort(key=lambda x: x[1], reverse=True)
        return similar_cases[:max_results]
    
    def add_new_record(self, name, age, contact, symptoms, condition, prescription, visit_time):
        new_record = {
            'name': name,
            'age': age,
            'contact': contact,
            'symptoms': symptoms,
            'condition': condition,
            'prescription': prescription,
            'visit_time': visit_time,
            'source_file': 'new_record'
        }
        self.all_records.append(new_record)
        self.save_new_record(new_record)
        return True
    
    def save_new_record(self, record: Dict):
        """保存新记录到Excel文件"""
        new_records_file = os.path.join(self.excel_folder_path, "new_records.xlsx")
        
        # 准备数据
        record_data = {
            '姓名': [record.get('name', '')],
            '年龄': [record.get('age', '')],
            '联系方式': [record.get('contact', '')],
            '症状': [record.get('symptoms', '')],
            '病情': [record.get('condition', '')],
            '处方': [record.get('prescription', '')],
            '就诊时间': [record.get('visit_time', '')]
        }
        
        try:
            if os.path.exists(new_records_file):
                # 如果文件存在，追加数据
                existing_df = pd.read_excel(new_records_file)
                new_df = pd.DataFrame(record_data)
                combined_df = pd.concat([existing_df, new_df], ignore_index=True)
            else:
                # 如果文件不存在，创建新文件
                combined_df = pd.DataFrame(record_data)
            
            combined_df.to_excel(new_records_file, index=False)
            
        except Exception as e:
            raise e


if __name__ == "__main__":
    app = MedicalRecordsGUI()
    app.run()